// components/FilterBar.jsx
import React, { useState } from "react";
import {
  Grid,
  Box,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Checkbox,
  Button,
  Slider,
  Typography,
  useMediaQuery,
  FormControlLabel,
  ToggleButtonGroup,
  ToggleButton,
  Rating,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";

const FilterBar = ({
  types = [],
  tags = [],
  tagCounts = {},
  displayTagToNormalized = {},
  onApply,
  priceMode,
  setPriceMode,
  isGeojsonVisible,
  setIsGeojsonVisible,
  modeType,
  setModeType,
  saleOrRent,
  setSaleOrRent,
  developers = [],
  developerCounts = {},
  ratingOptions = [5, 6, 7],
  ratingCounts = { 5: 0, 6: 0, 7: 0 },
  initialFilters,
  propSidebarOpen = false,
  onTogglePropSidebar = () => {},
  compact = false,
  sortMode = "priceHighLow",
  setSortMode = () => {},
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const [selectedType, setSelectedType] = useState(initialFilters.type || "");
  const [selectedTags, setSelectedTags] = useState(initialFilters.tags || []);
  const [selectedDevelopers, setSelectedDevelopers] = useState(
    initialFilters.developers || []
  );
  const [selectedRatings, setSelectedRatings] = useState(
    initialFilters.ratings || []
  );
  const [priceRange, setPriceRange] = useState(
    initialFilters.price || [0, 100000000]
  );
  const [dateFrom, setDateFrom] = useState(initialFilters.dateFrom || "");
  const [dateTo, setDateTo] = useState(initialFilters.dateTo || "");

  const [sort, setSort] = useState(sortMode || "priceHighLow");

  const sharedControls = {
    types,
    tags,
    priceMode,
    selectedType,
    setSelectedType,
    selectedTags,
    setSelectedTags,
    priceRange,
    setPriceRange,
    dateFrom,
    setDateFrom,
    dateTo,
    setDateTo,
    onApply,
    tagCounts,
    displayTagToNormalized,
    sort,
    setSort,
  };

  return (
    <Box
      sx={
        isMobile
          ? { px: 1, py: 0.5 }
          : {
              position: "absolute",
              top: 16,
              left: 16,
              bottom: 16,
              width: 270,
              minHeight: 400,
              bgcolor: "#181818",
              borderRadius: 2,
              boxShadow: 4,
              px: 2,
              py: 2,
              zIndex: 1200,
              display: "flex",
              flexDirection: "column",
              gap: 2,
              overflowY: "auto",
              overflowX: "hidden",
              "&::-webkit-scrollbar": {
                width: "6px",
                background: "transparent",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#000",
                borderRadius: 6,
                minHeight: 24,
              },
              "&::-webkit-scrollbar-track": {
                background: "transparent",
              },
            }
      }
    >
      {isMobile ? (
        <MobileView
          {...sharedControls}
          priceMode={priceMode}
          setPriceMode={setPriceMode}
          modeType={modeType}
          setModeType={setModeType}
          saleOrRent={saleOrRent}
          setSaleOrRent={setSaleOrRent}
          developers={developers}
          developerCounts={developerCounts}
          selectedDevelopers={selectedDevelopers}
          setSelectedDevelopers={setSelectedDevelopers}
          ratingOptions={ratingOptions}
          ratingCounts={ratingCounts}
          selectedRatings={selectedRatings}
          setSelectedRatings={setSelectedRatings}
        />
      ) : (
        <DesktopView
          {...sharedControls}
          priceMode={priceMode}
          setPriceMode={setPriceMode}
          isGeojsonVisible={isGeojsonVisible}
          setIsGeojsonVisible={setIsGeojsonVisible}
          modeType={modeType}
          setModeType={setModeType}
          saleOrRent={saleOrRent}
          setSaleOrRent={setSaleOrRent}
          developers={developers}
          developerCounts={developerCounts}
          selectedDevelopers={selectedDevelopers}
          setSelectedDevelopers={setSelectedDevelopers}
          ratingOptions={ratingOptions}
          ratingCounts={ratingCounts}
          selectedRatings={selectedRatings}
          setSelectedRatings={setSelectedRatings}
          propSidebarOpen={propSidebarOpen}
          onTogglePropSidebar={onTogglePropSidebar}
        />
      )}
    </Box>
  );
};

// Desktop sidebar
const DesktopView = ({
  types,
  tags,
  tagCounts,
  displayTagToNormalized,
  selectedType,
  setSelectedType,
  selectedTags,
  setSelectedTags,
  priceRange,
  setPriceRange,
  dateFrom,
  setDateFrom,
  dateTo,
  setDateTo,
  onApply,
  priceMode,
  setPriceMode,
  isGeojsonVisible,
  setIsGeojsonVisible,
  modeType,
  setModeType,
  saleOrRent,
  setSaleOrRent,
  developers,
  developerCounts,
  selectedDevelopers,
  setSelectedDevelopers,
  ratingOptions,
  ratingCounts,
  selectedRatings,
  setSelectedRatings,
  propSidebarOpen,
  onTogglePropSidebar,
  sort,
  setSort,
}) => (
  <Box
    sx={{
      display: "flex",
      flexDirection: "column",
      alignItems: "stretch",
      gap: 2,
      position: "fixed",
      top: 0,
      left: 0,
      bottom: 0,
      width: 320,
      height: "100vh",
      margin: 0,
      zIndex: 1200,
      background: "#181818",
      borderRight: "1px solid #222",
      boxShadow: "2px 0 8px rgba(0,0,0,0.12)",
      overflowY: "auto",
      overflowX: "hidden",
      padding: "24px 18px 18px 18px",
      boxSizing: "border-box",
      "&::-webkit-scrollbar": { width: "6px", background: "transparent" },
      "&::-webkit-scrollbar-thumb": {
        background: "#444",
        borderRadius: 6,
        minHeight: 24,
      },
      "&::-webkit-scrollbar-track": { background: "transparent" },
    }}
  >
    {/* Logo and minimalist sidebar toggle arrow */}
    <Box
      sx={{
        mb: 1.5,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
      }}
    >
      <img
        src="/2.svg"
        alt="Logo"
        style={{ height: 60, marginBottom: 0, display: "block" }}
      />
      <Box
        role="button"
        aria-label={
          propSidebarOpen ? "Hide property list" : "Show property list"
        }
        title={propSidebarOpen ? "Hide property list" : "Show property list"}
        onClick={onTogglePropSidebar}
        sx={{
          width: 32,
          height: 32,
          bgcolor: "#23272f",
          border: "none",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "#bbb",
          cursor: "pointer",
          boxShadow: "0 1px 4px rgba(0,0,0,.12)",
          userSelect: "none",
          fontSize: 18,
          transition: "background 0.2s",
          ml: 2,
        }}
      >
        {/* Chevron icon minimalist */}
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {propSidebarOpen ? (
            <path
              d="M11.5 14L7.5 9L11.5 4"
              stroke="#bbb"
              strokeWidth="2.2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          ) : (
            <path
              d="M6.5 4L10.5 9L6.5 14"
              stroke="#bbb"
              strokeWidth="2.2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}
        </svg>
      </Box>
    </Box>

    {/* Toggles */}
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 1,
        mb: 2,
        mt: -1,
        "& .MuiToggleButtonGroup-root": {
          width: "100%",
          minHeight: 38,
        },
        "& .MuiToggleButton-root": {
          color: "#aaa",
          border: "none",
          borderRadius: "8px !important",
          fontWeight: 600,
          px: 1.5,
          py: 0.5,
          fontSize: 13,
          minHeight: 36,
          minWidth: 0,
          flex: 1,
          transition: "background 0.2s",
        },
        "& .MuiToggleButtonGroup-grouped": {
          flex: 1,
        },
        "& .Mui-selected": {
          background: "#1b1b1b !important",
        },
      }}
    >
      <ToggleButtonGroup
        value={saleOrRent}
        exclusive
        onChange={(e, val) => {
          if (val !== null) setSaleOrRent(val);
        }}
        size="small"
        sx={{
          mb: 0.5,
          background: "#20232b",
          borderRadius: 2,
          width: "100%",
          gap: 0,
          "& .Mui-selected": { color: "#ffd600" },
        }}
      >
        <ToggleButton value="Sale">Sale</ToggleButton>
        <ToggleButton value="Rent">Rent</ToggleButton>
      </ToggleButtonGroup>

      <ToggleButtonGroup
        value={priceMode}
        exclusive
        onChange={(e, val) => {
          if (val) setPriceMode(val);
        }}
        size="small"
        sx={{
          mb: 0.5,
          background: "#20232b",
          borderRadius: 2,
          width: "100%",
          gap: 0,
          "& .Mui-selected": { color: "#e040fb" },
        }}
      >
        <ToggleButton value="value">By Value</ToggleButton>
        <ToggleButton value="perSqft">Per Sqft</ToggleButton>
      </ToggleButtonGroup>

      <ToggleButtonGroup
        value={isGeojsonVisible ? "geojson" : "cluster"}
        exclusive
        onChange={(e, val) => {
          if (val !== null) setIsGeojsonVisible(val === "geojson");
        }}
        size="small"
        sx={{
          mb: 0.5,
          background: "#20232b",
          borderRadius: 2,
          width: "100%",
          gap: 0,
          "& .Mui-selected": { color: "#29b6f6" },
        }}
      >
        <ToggleButton value="geojson">Area</ToggleButton>
        <ToggleButton value="cluster">Cluster</ToggleButton>
      </ToggleButtonGroup>

      <ToggleButtonGroup
        value={modeType}
        exclusive
        onChange={(e, val) => {
          if (val) setModeType(val);
        }}
        size="small"
        sx={{
          mb: 0,
          background: "#20232b",
          borderRadius: 2,
          width: "100%",
          gap: 0,
          "& .Mui-selected": { color: "#00e676" },
        }}
      >
        <ToggleButton value="Live">Live</ToggleButton>
        <ToggleButton value="Transactions">Transactions</ToggleButton>
      </ToggleButtonGroup>
    </Box>

    {/* Property Type */}
    <Box sx={{ mb: 2 }}>
      <Typography
        variant="subtitle2"
        sx={{ color: "#fff", fontWeight: 600, fontSize: 15, mb: 1, pl: 0.5 }}
      >
        Property Type
      </Typography>
      <Box sx={sectionBox}>
        {types.map((type) => (
          <FormControlLabel
            key={type}
            control={
              <Checkbox
                size="small"
                checked={selectedType === type}
                onChange={() => {
                  const newType = type === selectedType ? "" : type;
                  setSelectedType(newType);
                  onApply({
                    type: newType,
                    tags: selectedTags,
                    price: priceRange,
                    dateFrom,
                    dateTo,
                    priceMode,
                    developers: selectedDevelopers,
                    ratings: selectedRatings,
                  });
                }}
                sx={{ color: "#ccc", p: 0.5 }}
              />
            }
            label={
              <Typography sx={{ fontSize: 13, color: "#ccc", pl: 0.5 }}>
                {type}
              </Typography>
            }
            sx={{ m: 0, width: "100%" }}
          />
        ))}
      </Box>
    </Box>

    {/* Popular Filters */}
    <Box sx={{ mb: 2 }}>
      <Typography
        variant="subtitle2"
        sx={{ color: "#fff", fontWeight: 600, fontSize: 15, mb: 1, pl: 0.5 }}
      >
        Popular Filters
      </Typography>
      <Box sx={sectionBox}>
        {tags.map((tag) => (
          <FormControlLabel
            key={tag}
            control={
              <Checkbox
                size="small"
                checked={selectedTags.includes(tag)}
                onChange={() => {
                  const newTags = selectedTags.includes(tag)
                    ? selectedTags.filter((t) => t !== tag)
                    : [...selectedTags, tag];
                  setSelectedTags(newTags);
                  onApply({
                    type: selectedType,
                    tags: newTags,
                    price: priceRange,
                    dateFrom,
                    dateTo,
                    priceMode,
                    developers: selectedDevelopers,
                    ratings: selectedRatings,
                  });
                }}
                sx={{ color: "#ccc", p: 0.5 }}
              />
            }
            label={
              <Box
                sx={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                <Typography sx={{ fontSize: 13, color: "#ccc", pl: 0.5 }}>
                  {tag}{" "}
                  <span style={{ color: "#888", fontSize: 13 }}>
                    {(tagCounts?.[displayTagToNormalized?.[tag]] ?? 0)
                      .toLocaleString("en-US")
                      .replace(/,/g, " ")}
                  </span>
                </Typography>
              </Box>
            }
            sx={{ m: 0, width: "100%" }}
          />
        ))}
      </Box>
    </Box>

    {/* Developers */}
    <Box sx={{ mb: 2 }}>
      <Typography
        variant="subtitle2"
        sx={{ color: "#fff", fontWeight: 600, fontSize: 15, mb: 1, pl: 0.5 }}
      >
        Developers
      </Typography>
      <Box sx={sectionBox}>
        {developers.map((dev) => (
          <FormControlLabel
            key={dev}
            control={
              <Checkbox
                size="small"
                checked={selectedDevelopers.includes(dev)}
                onChange={() => {
                  const newDevs = selectedDevelopers.includes(dev)
                    ? selectedDevelopers.filter((d) => d !== dev)
                    : [...selectedDevelopers, dev];
                  setSelectedDevelopers(newDevs);
                  onApply({
                    type: selectedType,
                    tags: selectedTags,
                    price: priceRange,
                    dateFrom,
                    dateTo,
                    priceMode,
                    developers: newDevs,
                    ratings: selectedRatings,
                  });
                }}
                sx={{ color: "#ccc", p: 0.5 }}
              />
            }
            label={
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  width: "100%",
                  justifyContent: "space-between",
                }}
              >
                <Typography
                  sx={{
                    fontSize: 13,
                    color: "#ccc",
                    pl: 0.5,
                    pr: 1,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {dev}
                </Typography>
                <Typography sx={{ fontSize: 12, color: "#888" }}>
                  {developerCounts?.[dev] ?? 0}
                </Typography>
              </Box>
            }
            sx={{ m: 0, width: "100%" }}
          />
        ))}
      </Box>
    </Box>

    {/* Rating (5,6,7) with stars */}
    <Box sx={{ mb: 2 }}>
      <Typography
        variant="subtitle2"
        sx={{ color: "#fff", fontWeight: 600, fontSize: 15, mb: 1, pl: 0.5 }}
      >
        Rating
      </Typography>
      <Box sx={sectionBox}>
        {ratingOptions.map((r) => (
          <FormControlLabel
            key={r}
            control={
              <Checkbox
                size="small"
                checked={selectedRatings.includes(r)}
                onChange={() => {
                  const newRatings = selectedRatings.includes(r)
                    ? selectedRatings.filter((x) => x !== r)
                    : [...selectedRatings, r];
                  setSelectedRatings(newRatings);
                  onApply({
                    type: selectedType,
                    tags: selectedTags,
                    price: priceRange,
                    dateFrom,
                    dateTo,
                    priceMode,
                    developers: selectedDevelopers,
                    ratings: newRatings,
                  });
                }}
                sx={{ color: "#ccc", p: 0.5 }}
              />
            }
            label={
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <Rating value={r} max={7} readOnly size="small" />
                <Typography sx={{ fontSize: 12, color: "#888", ml: 1 }}>
                  {ratingCounts?.[r] ?? 0}
                </Typography>
              </Box>
            }
            sx={{ m: 0, width: "100%" }}
          />
        ))}
      </Box>
    </Box>

    {/* Price */}
    <Box sx={{ mb: 2 }}>
      <PriceSlider
        priceMode={priceMode}
        priceRange={priceRange}
        setPriceRange={(range) => {
          setPriceRange(range);
          onApply({
            type: selectedType,
            tags: selectedTags,
            price: range,
            dateFrom,
            dateTo,
            priceMode,
            developers: selectedDevelopers,
            ratings: selectedRatings,
          });
        }}
      />
    </Box>

    {/* Date */}
    <Box>
      <DatePickers
        dateFrom={dateFrom}
        setDateFrom={(val) => {
          setDateFrom(val);
          onApply({
            type: selectedType,
            tags: selectedTags,
            price: priceRange,
            dateFrom: val,
            dateTo,
            priceMode,
            developers: selectedDevelopers,
            ratings: selectedRatings,
          });
        }}
        dateTo={dateTo}
        setDateTo={(val) => {
          setDateTo(val);
          onApply({
            type: selectedType,
            tags: selectedTags,
            price: priceRange,
            dateFrom,
            dateTo: val,
            priceMode,
            developers: selectedDevelopers,
            ratings: selectedRatings,
          });
        }}
      />
    </Box>
  </Box>
);

const sectionBox = {
  maxHeight: 140,
  overflowY: "auto",
  bgcolor: "#181818",
  borderRadius: 1,
  border: "1px solid #222",
  p: 1,
  mb: 1,
  display: "flex",
  flexDirection: "column",
  gap: 0.5,
  "&::-webkit-scrollbar": { width: "6px", background: "transparent" },
  "&::-webkit-scrollbar-thumb": {
    background: "#444",
    borderRadius: 6,
    minHeight: 24,
  },
  "&::-webkit-scrollbar-track": { background: "transparent" },
};

// Mobile View - Completely redesigned for the new layout
const MobileView = ({
  types,
  tags,
  selectedType,
  setSelectedType,
  selectedTags,
  setSelectedTags,
  priceRange,
  setPriceRange,
  dateFrom,
  setDateFrom,
  dateTo,
  setDateTo,
  priceMode,
  setPriceMode,
  onApply,
  tagCounts = {},
  displayTagToNormalized = {},
  modeType,
  setModeType,
  saleOrRent,
  setSaleOrRent,
  developers = [],
  developerCounts = {},
  selectedDevelopers,
  setSelectedDevelopers,
  ratingOptions = [5, 6, 7],
  ratingCounts = { 5: 0, 6: 0, 7: 0 },
  selectedRatings,
  setSelectedRatings,
}) => {
  const [showFilters, setShowFilters] = useState(false);

  const handleApplyFilters = () => {
    onApply({
      type: selectedType,
      tags: selectedTags,
      price: priceRange,
      dateFrom,
      dateTo,
      priceMode,
      developers: selectedDevelopers,
      ratings: selectedRatings,
    });
  };

  return (
    <Box>
      {/* Show Filters Button */}
      <Box sx={{ mb: 1 }}>
        <Button
          onClick={() => setShowFilters(!showFilters)}
          variant="outlined"
          fullWidth
          sx={{
            color: "#00ffcc",
            borderColor: "#00ffcc",
            fontSize: 12,
            fontWeight: 600,
            py: 1,
            "&:hover": {
              borderColor: "#00ffcc",
              bgcolor: "rgba(0, 255, 204, 0.1)",
            },
          }}
        >
          {showFilters ? "Hide Filters" : "Show Filters"}
        </Button>
      </Box>

      {/* Toggle Controls - Always visible */}
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1, mb: 2 }}>
        {/* Live/Transactions */}
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={() => setModeType("Live")}
            variant={modeType === "Live" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: modeType === "Live" ? "#00e676" : "transparent",
              color: modeType === "Live" ? "#000" : "#00e676",
              borderColor: "#00e676",
              "&:hover": {
                bgcolor:
                  modeType === "Live" ? "#00e676" : "rgba(0, 230, 118, 0.1)",
                borderColor: "#00e676",
              },
            }}
          >
            LIVE
          </Button>
          <Button
            onClick={() => setModeType("Transactions")}
            variant={modeType === "Transactions" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: modeType === "Transactions" ? "#00e676" : "transparent",
              color: modeType === "Transactions" ? "#000" : "#00e676",
              borderColor: "#00e676",
              "&:hover": {
                bgcolor:
                  modeType === "Transactions"
                    ? "#00e676"
                    : "rgba(0, 230, 118, 0.1)",
                borderColor: "#00e676",
              },
            }}
          >
            TRANSACTIONS
          </Button>
        </Box>

        {/* By Value/Per Sqft */}
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={() => setPriceMode("value")}
            variant={priceMode === "value" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: priceMode === "value" ? "#e040fb" : "transparent",
              color: priceMode === "value" ? "#000" : "#e040fb",
              borderColor: "#e040fb",
              "&:hover": {
                bgcolor:
                  priceMode === "value" ? "#e040fb" : "rgba(224, 64, 251, 0.1)",
                borderColor: "#e040fb",
              },
            }}
          >
            BY VALUE
          </Button>
          <Button
            onClick={() => setPriceMode("perSqft")}
            variant={priceMode === "perSqft" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: priceMode === "perSqft" ? "#e040fb" : "transparent",
              color: priceMode === "perSqft" ? "#000" : "#e040fb",
              borderColor: "#e040fb",
              "&:hover": {
                bgcolor:
                  priceMode === "perSqft"
                    ? "#e040fb"
                    : "rgba(224, 64, 251, 0.1)",
                borderColor: "#e040fb",
              },
            }}
          >
            PER SQFT
          </Button>
        </Box>

        {/* Sale/Rent */}
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={() => setSaleOrRent("Sale")}
            variant={saleOrRent === "Sale" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: saleOrRent === "Sale" ? "#ffd600" : "transparent",
              color: saleOrRent === "Sale" ? "#000" : "#ffd600",
              borderColor: "#ffd600",
              "&:hover": {
                bgcolor:
                  saleOrRent === "Sale" ? "#ffd600" : "rgba(255, 214, 0, 0.1)",
                borderColor: "#ffd600",
              },
            }}
          >
            SALE
          </Button>
          <Button
            onClick={() => setSaleOrRent("Rent")}
            variant={saleOrRent === "Rent" ? "contained" : "outlined"}
            size="small"
            sx={{
              flex: 1,
              fontSize: 11,
              py: 0.5,
              bgcolor: saleOrRent === "Rent" ? "#ffd600" : "transparent",
              color: saleOrRent === "Rent" ? "#000" : "#ffd600",
              borderColor: "#ffd600",
              "&:hover": {
                bgcolor:
                  saleOrRent === "Rent" ? "#ffd600" : "rgba(255, 214, 0, 0.1)",
                borderColor: "#ffd600",
              },
            }}
          >
            RENT
          </Button>
        </Box>
      </Box>

      {/* Filters Panel - Only visible when showFilters is true */}
      {showFilters && (
        <Box
          sx={{
            bgcolor: "#181818",
            border: "1px solid #333",
            borderRadius: 2,
            p: 2,
            maxHeight: "60vh",
            overflowY: "auto",
            "&::-webkit-scrollbar": { width: "6px" },
            "&::-webkit-scrollbar-thumb": {
              background: "#444",
              borderRadius: 6,
            },
          }}
        >
          {/* Type */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Type
            </Typography>
            <FormControl fullWidth size="small">
              <Select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                sx={{
                  fontSize: 12,
                  color: "#ccc",
                  "& .MuiOutlinedInput-notchedOutline": { borderColor: "#444" },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#666",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#00bfa5",
                  },
                }}
                MenuProps={{
                  PaperProps: { sx: { bgcolor: "#111", color: "#ccc" } },
                }}
              >
                <MenuItem value="">All</MenuItem>
                {types.map((type, i) => (
                  <MenuItem key={i} value={type} sx={{ fontSize: 12 }}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Price */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Price (AED)
            </Typography>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                gap: 1,
                mb: 1,
              }}
            >
              {[0, 1].map((i) => (
                <Box
                  key={i}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    border: "1px solid #444",
                    borderRadius: 1,
                    px: 1,
                    py: 0.5,
                    width: "100%",
                  }}
                >
                  <input
                    type="text"
                    value={priceRange[i].toLocaleString()}
                    onChange={(e) => {
                      const val = Number(e.target.value.replace(/,/g, ""));
                      if (!isNaN(val)) {
                        setPriceRange(
                          i === 0 ? [val, priceRange[1]] : [priceRange[0], val]
                        );
                      }
                    }}
                    style={{
                      background: "transparent",
                      border: "none",
                      outline: "none",
                      color: "white",
                      fontSize: 11,
                      width: "100%",
                    }}
                  />
                  <Typography sx={{ fontSize: 10, color: "#ccc", ml: 0.5 }}>
                    AED
                  </Typography>
                </Box>
              ))}
            </Box>
            <Slider
              value={priceRange}
              onChange={(e, val) => setPriceRange(val)}
              min={0}
              max={priceMode === "perSqft" ? 10000 : 100000000}
              step={priceMode === "perSqft" ? 100 : 1000}
              size="small"
              valueLabelDisplay="auto"
              valueLabelFormat={(val) => val.toLocaleString()}
              sx={{ color: "#00ffcc", height: 3 }}
            />
          </Box>

          {/* Date */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Date
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              {[
                { label: "From", value: dateFrom, set: setDateFrom },
                { label: "To", value: dateTo, set: setDateTo },
              ].map(({ label, value, set }) => (
                <DatePicker
                  key={label}
                  label={label}
                  value={value ? dayjs(value) : null}
                  onChange={(val) => set(val ? val.format("YYYY-MM-DD") : "")}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      sx: {
                        "& .MuiInputBase-input": {
                          color: "#bbb",
                          fontSize: 11,
                        },
                        "& .MuiInputLabel-root": {
                          color: "#aaa",
                          fontSize: 11,
                        },
                        "& .MuiSvgIcon-root": { color: "#ccc" },
                        "& .MuiOutlinedInput-root": {
                          "& fieldset": { borderColor: "#444" },
                          "&:hover fieldset": { borderColor: "#666" },
                          "&.Mui-focused fieldset": { borderColor: "#00bfa5" },
                        },
                      },
                    },
                    popper: {
                      sx: {
                        "& .MuiPaper-root": {
                          bgcolor: "#121212",
                          color: "#eee",
                          borderRadius: 2,
                        },
                        "& .MuiPickersCalendarHeader-root, & .MuiDayCalendar-weekDayLabel, & .MuiPickersDay-root":
                          {
                            color: "#ccc",
                          },
                        "& .MuiPickersDay-root.Mui-selected": {
                          bgcolor: "#00bfa5",
                          color: "#fff",
                        },
                        "& .MuiPickersDay-root:hover": { bgcolor: "#333" },
                      },
                    },
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* Property Type */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Property Type
            </Typography>
            <Box
              sx={{
                maxHeight: 120,
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              {types.map((type) => (
                <FormControlLabel
                  key={type}
                  control={
                    <Checkbox
                      size="small"
                      checked={selectedType === type}
                      onChange={() => {
                        const newType = type === selectedType ? "" : type;
                        setSelectedType(newType);
                      }}
                      sx={{ color: "#ccc", p: 0.5 }}
                    />
                  }
                  label={
                    <Typography sx={{ fontSize: 11, color: "#ccc" }}>
                      {type}
                    </Typography>
                  }
                  sx={{ m: 0 }}
                />
              ))}
            </Box>
          </Box>

          {/* Popular Filters */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Popular Filters
            </Typography>
            <Box
              sx={{
                maxHeight: 120,
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              {tags.map((tag) => (
                <FormControlLabel
                  key={tag}
                  control={
                    <Checkbox
                      size="small"
                      checked={selectedTags.includes(tag)}
                      onChange={() => {
                        const newTags = selectedTags.includes(tag)
                          ? selectedTags.filter((t) => t !== tag)
                          : [...selectedTags, tag];
                        setSelectedTags(newTags);
                      }}
                      sx={{ color: "#ccc", p: 0.5 }}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        width: "100%",
                      }}
                    >
                      <Typography sx={{ fontSize: 11, color: "#ccc" }}>
                        {tag}
                      </Typography>
                      <Typography sx={{ fontSize: 10, color: "#888", ml: 1 }}>
                        {tagCounts?.[displayTagToNormalized?.[tag]] ?? 0}
                      </Typography>
                    </Box>
                  }
                  sx={{ m: 0 }}
                />
              ))}
            </Box>
          </Box>

          {/* Developers */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Developers
            </Typography>
            <Box
              sx={{
                maxHeight: 120,
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              {developers.map((dev) => (
                <FormControlLabel
                  key={dev}
                  control={
                    <Checkbox
                      size="small"
                      checked={selectedDevelopers.includes(dev)}
                      onChange={() => {
                        const newDevs = selectedDevelopers.includes(dev)
                          ? selectedDevelopers.filter((d) => d !== dev)
                          : [...selectedDevelopers, dev];
                        setSelectedDevelopers(newDevs);
                      }}
                      sx={{ color: "#ccc", p: 0.5 }}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        width: "100%",
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: 11,
                          color: "#ccc",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {dev}
                      </Typography>
                      <Typography sx={{ fontSize: 10, color: "#888" }}>
                        {developerCounts?.[dev] ?? 0}
                      </Typography>
                    </Box>
                  }
                  sx={{ m: 0 }}
                />
              ))}
            </Box>
          </Box>

          {/* Rating */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="subtitle2"
              sx={{ color: "#fff", fontWeight: 600, fontSize: 14, mb: 1 }}
            >
              Rating
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
              {ratingOptions.map((r) => (
                <FormControlLabel
                  key={r}
                  control={
                    <Checkbox
                      size="small"
                      checked={selectedRatings.includes(r)}
                      onChange={() => {
                        const newRatings = selectedRatings.includes(r)
                          ? selectedRatings.filter((x) => x !== r)
                          : [...selectedRatings, r];
                        setSelectedRatings(newRatings);
                      }}
                      sx={{ color: "#ccc", p: 0.5 }}
                    />
                  }
                  label={
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        width: "100%",
                      }}
                    >
                      <Rating value={r} max={7} readOnly size="small" />
                      <Typography sx={{ fontSize: 10, color: "#888", ml: 1 }}>
                        {ratingCounts?.[r] ?? 0}
                      </Typography>
                    </Box>
                  }
                  sx={{ m: 0 }}
                />
              ))}
            </Box>
          </Box>

          {/* Apply Button */}
          <Button
            onClick={handleApplyFilters}
            variant="contained"
            fullWidth
            sx={{
              bgcolor: "#00bfa5",
              color: "#fff",
              fontSize: 12,
              fontWeight: 600,
              py: 1.5,
              "&:hover": {
                bgcolor: "#00a693",
              },
            }}
          >
            APPLY
          </Button>
        </Box>
      )}
    </Box>
  );
};

const PriceSlider = ({ priceMode, priceRange, setPriceRange }) => (
  <>
    <Typography variant="caption" color="#ccc">
      {priceMode === "perSqft" ? "Price (AED/sqft)" : "Price (AED)"}
    </Typography>
    <Box
      sx={{ display: "flex", justifyContent: "space-between", gap: 1, mb: 0.5 }}
    >
      {[0, 1].map((i) => (
        <Box
          key={i}
          sx={{
            display: "flex",
            alignItems: "center",
            border: "1px solid #444",
            borderRadius: 1,
            px: 1,
            py: 0.4,
            width: "100%",
          }}
        >
          <input
            type="text"
            value={priceRange[i].toLocaleString()}
            onChange={(e) => {
              const val = Number(e.target.value.replace(/,/g, ""));
              if (!isNaN(val)) {
                setPriceRange(
                  i === 0 ? [val, priceRange[1]] : [priceRange[0], val]
                );
              }
            }}
            style={{
              background: "transparent",
              border: "none",
              outline: "none",
              color: "white",
              fontSize: 12,
              width: "100%",
            }}
          />
          <Typography sx={{ fontSize: 11, color: "#ccc", ml: 0.5 }}>
            AED
          </Typography>
        </Box>
      ))}
    </Box>
    <Slider
      value={priceRange}
      onChange={(e, val) => setPriceRange(val)}
      min={0}
      max={priceMode === "perSqft" ? 10000 : 100000000}
      step={priceMode === "perSqft" ? 100 : 1000}
      size="small"
      valueLabelDisplay="auto"
      valueLabelFormat={(val) => val.toLocaleString()}
      sx={{ color: "#00ffcc", height: 3, mt: 0 }}
    />
  </>
);

const DatePickers = ({ dateFrom, setDateFrom, dateTo, setDateTo }) => (
  <>
    <Typography variant="caption" sx={{ color: "#aaa", fontSize: 11 }}>
      Date
    </Typography>
    <Box
      sx={{
        display: "flex",
        gap: 1,
        flexWrap: "nowrap",
        flexDirection: "row",
        mt: 0.5,
        overflowX: "auto",
      }}
    >
      {[
        { label: "From", value: dateFrom, set: setDateFrom },
        { label: "To", value: dateTo, set: setDateTo },
      ].map(({ label, value, set }) => (
        <DatePicker
          key={label}
          label={label}
          value={value ? dayjs(value) : null}
          onChange={(val) => set(val ? val.format("YYYY-MM-DD") : "")}
          slotProps={{
            textField: {
              size: "small",
              fullWidth: true,
              sx: {
                width: 120,
                "& .MuiInputBase-input": { color: "#bbb", fontSize: 12 },
                label: { color: "#aaa", fontSize: 11 },
                svg: { color: "#ccc" },
                "& .MuiOutlinedInput-root": {
                  "& fieldset": { borderColor: "#444" },
                  "&:hover fieldset": { borderColor: "#666" },
                  "&.Mui-focused fieldset": { borderColor: "#00bfa5" },
                },
              },
            },
            popper: {
              sx: {
                "& .MuiPaper-root": {
                  bgcolor: "#121212",
                  color: "#eee",
                  borderRadius: 2,
                },
                "& .MuiPickersCalendarHeader-root, & .MuiDayCalendar-weekDayLabel, & .MuiPickersDay-root":
                  {
                    color: "#ccc",
                  },
                "& .MuiPickersDay-root.Mui-selected": {
                  bgcolor: "#00bfa5",
                  color: "#fff",
                },
                "& .MuiPickersDay-root:hover": { bgcolor: "#333" },
              },
            },
          }}
        />
      ))}
    </Box>
  </>
);

export default FilterBar;

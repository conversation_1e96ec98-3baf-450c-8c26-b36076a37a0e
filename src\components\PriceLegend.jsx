import React from 'react';
import { Box, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';

const PriceLegend = ({ min, avg, max, priceMode }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Set the unit label
  const unit = priceMode === 'perSqft' ? 'AED/sqft' : 'AED';

  return (
    <Box
      sx={{
        position: 'absolute',
        bottom: isMobile ? 70 : 6,
        right: 10,
        backgroundColor: '#1e1e1e',
        color: 'white',
        borderRadius: 2,
        padding: isMobile ? '8px 10px' : '12px 16px',
        zIndex: 998,
        boxShadow: '0 2px 6px rgba(0,0,0,0.35)',
        fontSize: isMobile ? 11 : 13,
        minWidth: isMobile ? 160 : 200,
        lineHeight: 1.4
      }}
    >
      <Typography
        variant="subtitle2"
        sx={{
          fontSize: isMobile ? 11.5 : 13.5,
          fontWeight: 600,
          marginBottom: 1
        }}
      >
        Legend ({priceMode === 'perSqft' ? 'Per Sq.Ft' : 'By Value'})
      </Typography>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
        <Typography variant="body2">
          <span style={{ color: '#00ff00', fontWeight: 700 }}>●</span> Low: {min?.toLocaleString()} {unit}
        </Typography>
        <Typography variant="body2">
          <span style={{ color: '#ffff00', fontWeight: 700 }}>●</span> Avg: {avg?.toLocaleString()} {unit}
        </Typography>
        <Typography variant="body2">
          <span style={{ color: '#ff007f', fontWeight: 700 }}>●</span> High: {max?.toLocaleString()} {unit}
        </Typography>
      </Box>
    </Box>
  );
};

export default PriceLegend;

import React, { useEffect, useState } from "react";
import ClusterMap from "./components/ClusterMap";
import FilterBar from "./components/FilterBar";
import SearchBar from "./components/SearchBar";
import PriceLegend from "./components/PriceLegend";
import PropertySidebar from "./components/PropertySidebar";
import { Box, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

const App = () => {
  const [propertyData, setPropertyData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [flyToLocation, setFlyToLocation] = useState(null);
  const [priceStats, setPriceStats] = useState({ min: 0, avg: 0, max: 0 });
  const [basemap, setBasemap] = useState("mapbox://styles/mapbox/dark-v10");
  const [geojsonData, setGeojsonData] = useState(null);
  const [priceMode, setPriceMode] = useState("value");
  const [isGeojsonVisible, setIsGeojsonVisible] = useState(false);
  const [modeType, setModeType] = useState("Transactions");
  const [loading, setLoading] = useState(true);
  const [saleOrRent, setSaleOrRent] = useState("");
  const [propSidebarOpen, setPropSidebarOpen] = useState(false);
  const [sortMode, setSortMode] = useState("priceHighLow");

  const [filters, setFilters] = useState({
    type: "",
    tags: [],
    price: [0, 100000000],
    dateFrom: "",
    dateTo: "",
    priceMode: "value",
    developers: [],
    ratings: [], // 5, 6, 7 only
    sortMode: "priceHighLow",
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Load property data (unchanged source; now also pulls optional Image)
  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetch("https://dubai-estate-nldvnw.fly.dev/api/gamma-property");
        const res = await fetch(
          "https://dubai-estate-nldvnw.fly.dev/gamma_property.json"
        );
        const geojson = await res.json();

        const parsed = geojson.features
          .map((f) => {
            if (!f.geometry || !f.geometry.coordinates) return null;
            const props = f.properties;
            const [lng, lat] = f.geometry.coordinates;
            const dev =
              (props["Developer"] || "").toString().trim() || "Unknown";

            // Normalize rating to number; supports decimals like "8.2" if present in your feed
            let ratingRaw = props["Rating"];
            let rating = null;
            if (
              ratingRaw !== undefined &&
              ratingRaw !== null &&
              String(ratingRaw).trim() !== ""
            ) {
              const n = Number(String(ratingRaw).replace(/[^\d.]/g, ""));
              rating = isNaN(n) ? null : n;
            }

            return {
              lat,
              lng,
              location: props["Location"],
              propertyType: props["PROPERTY TYPE"],
              soldFor: parseFloat(
                (props["Value"] || "").replace(/[^0-9]/g, "")
              ),
              sqft: props["Sqft"],
              developer: dev,
              listingUrl: props["Link to Listing"],
              date: props["DateSold By"],
              specs: props["Main Tags"],
              status: props["Live / Transactions"],
              saleStatus: props["Sale / Rent"],
              rating, // numeric or null
              image: props["Image"] || "", // NEW: optional image URL
              comments: [
                props["Comment 2 "],
                props["Comment 3"],
                props["Comment 4"],
              ]
                .filter(Boolean)
                .join(", "),
            };
          })
          .filter(Boolean);

        setPropertyData(parsed);
        setFilteredData(parsed);
        setLoading(false);
      } catch {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Load boundary GeoJSON (optional overlay)
  useEffect(() => {
    fetch("/Dubai.geojson")
      .then((res) => res.json())
      .then((data) => setGeojsonData(data))
      .catch(() => {});
  }, []);

  // Filter and sort property data based on various criteria
  useEffect(() => {
    let results = [...propertyData];

    results = results.filter(
      (d) =>
        (modeType === "Live" && d.status === "Live") ||
        (modeType === "Transactions" && d.status === "Transactions")
    );

    if (saleOrRent) {
      results = results.filter((d) => d.saleStatus === saleOrRent);
    }

    if (filters.type) {
      results = results.filter((d) => d.propertyType === filters.type);
    }

    if (filters.tags.length > 0) {
      results = results.filter((d) =>
        filters.tags.some(
          (tag) =>
            (d.specs && d.specs.toLowerCase().includes(tag.toLowerCase())) ||
            (d.comments && d.comments.toLowerCase().includes(tag.toLowerCase()))
        )
      );
    }

    if (filters.developers && filters.developers.length > 0) {
      results = results.filter((d) => {
        const name = d.developer?.trim() || "Unknown";
        return filters.developers.includes(name);
      });
    }

    if (filters.ratings && filters.ratings.length > 0) {
      results = results.filter((d) => {
        if (d.rating == null) return false;
        return filters.ratings.includes(d.rating);
      });
    }

    results = results.filter((d) => {
      const sqft = parseFloat(d.sqft?.toString().replace(/[^\d.]/g, "")) || 1;
      const value =
        filters.priceMode === "perSqft"
          ? Math.round((d.soldFor || 0) / sqft)
          : d.soldFor || 0;
      return value >= filters.price[0] && value <= filters.price[1];
    });

    if (filters.dateFrom) {
      results = results.filter(
        (d) => new Date(d.date) >= new Date(filters.dateFrom)
      );
    }

    if (filters.dateTo) {
      results = results.filter(
        (d) => new Date(d.date) <= new Date(filters.dateTo)
      );
    }

    if (searchValue.trim()) {
      results = results.filter((d) =>
        (d.location || "").toLowerCase().includes(searchValue.toLowerCase())
      );
    }

    // Sort results according to sortMode
    switch (filters.sortMode) {
      case "priceHighLow":
        results.sort((a, b) => (b.soldFor || 0) - (a.soldFor || 0));
        break;
      case "priceLowHigh":
        results.sort((a, b) => (a.soldFor || 0) - (b.soldFor || 0));
        break;
      case "ratingHighLow":
        results.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case "ratingLowHigh":
        results.sort((a, b) => (a.rating || 0) - (b.rating || 0));
        break;
      default:
        break;
    }

    setFilteredData(results);
  }, [filters, propertyData, searchValue, modeType, saleOrRent]);

  // Location autocomplete options
  const locationOptions = Array.from(
    new Map(
      propertyData.map((d) => [
        d.location,
        { label: d.location, lat: d.lat, lng: d.lng },
      ])
    ).values()
  );

  const propertyTypes = [
    ...new Set(propertyData.map((d) => d.propertyType).filter(Boolean)),
  ];

  // Tags
  const tagCounts = {};
  const displayTagToNormalized = {};
  const tagSet = new Set();
  propertyData.forEach((d) => {
    const tagsArr = [];
    if (d.specs) tagsArr.push(...d.specs.split(","));
    if (d.comments) tagsArr.push(...d.comments.split(","));
    tagsArr
      .map((t) => t.trim())
      .filter(Boolean)
      .forEach((tag) => {
        const norm = tag.toLowerCase();
        tagCounts[norm] = (tagCounts[norm] || 0) + 1;
        tagSet.add(tag);
      });
  });
  const extractedTags = Array.from(tagSet);
  extractedTags.forEach((tag) => {
    displayTagToNormalized[tag] = tag.toLowerCase();
  });

  // Developers
  const developerCounts = {};
  const developerSet = new Set();
  propertyData.forEach((d) => {
    const name = d.developer?.trim() || "Unknown";
    developerCounts[name] = (developerCounts[name] || 0) + 1;
    developerSet.add(name);
  });
  const developers = Array.from(developerSet).sort();

  // Ratings counts (if you still want 5/6/7 buckets)
  const ratingCounts = { 5: 0, 6: 0, 7: 0 };
  propertyData.forEach((d) => {
    if (d.rating && ratingCounts[d.rating] !== undefined) {
      ratingCounts[d.rating] += 1;
    }
  });
  const ratingOptions = [5, 6, 7];

  const handleLocationSelect = (loc) => {
    const found = propertyData.find((d) => d.location === loc);
    if (found) {
      setFlyToLocation({ lat: found.lat, lng: found.lng });
    }
  };

  const handleSidebarSelect = (item) => {
    if (item?.lat && item?.lng) {
      setFlyToLocation({ lat: item.lat, lng: item.lng });
    }
  };

  if (loading) {
    return (
      <Box
        height="100vh"
        display="flex"
        alignItems="center"
        justifyContent="center"
        bgcolor="#000"
      >
        <img
          src="/1.svg"
          alt="Loading..."
          style={{
            width: 150,
            height: 150,
            animation: "pulse 2s infinite ease-in-out",
          }}
        />
        <style>
          {`
            @keyframes pulse {
              0% { transform: scale(1); opacity: 0.7; }
              50% { transform: scale(1.1); opacity: 1; }
              100% { transform: scale(1); opacity: 0.7; }
          `}
        </style>
      </Box>
    );
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100vh" }}>
      {/* Mobile Layout */}
      {isMobile ? (
        <>
          {/* Mobile Header */}
          <Box
            sx={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              height: 60,
              bgcolor: "#181818",
              zIndex: 1300,
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              px: 2,
              borderBottom: "1px solid #333",
            }}
          >
            {/* Logo */}
            <Box sx={{ height: 40 }}>
              <img src="/2.svg" alt="Logo" style={{ height: "100%" }} />
            </Box>

            {/* Search Bar */}
            <Box sx={{ flex: 1, mx: 2, maxWidth: 200 }}>
              <SearchBar
                value={searchValue}
                onChange={setSearchValue}
                onSelect={handleLocationSelect}
                options={locationOptions}
                compact={true}
                setFlyToLocation={setFlyToLocation}
              />
            </Box>

            {/* Basemap Selector */}
            <Box sx={{ minWidth: 80, bgcolor: "#222", borderRadius: 1 }}>
              <select
                value={basemap}
                onChange={(e) => setBasemap(e.target.value)}
                style={{
                  color: "#ccc",
                  background: "transparent",
                  border: "none",
                  fontSize: 11,
                  padding: "6px 8px",
                  borderRadius: 6,
                  width: "100%",
                }}
              >
                <option value="mapbox://styles/mapbox/dark-v10">Dark</option>
                <option value="mapbox://styles/mapbox/light-v10">Light</option>
                <option value="mapbox://styles/mapbox/streets-v11">
                  Streets
                </option>
                <option value="mapbox://styles/mapbox/satellite-streets-v12">
                  Satellite
                </option>
              </select>
            </Box>
          </Box>

          {/* Mobile Filter Controls */}
          <Box
            sx={{
              position: "fixed",
              top: 60,
              left: 0,
              right: 0,
              bgcolor: "#181818",
              zIndex: 1200,
              px: 2,
              py: 1,
              borderBottom: "1px solid #333",
            }}
          >
            <FilterBar
              types={propertyTypes}
              tags={extractedTags}
              tagCounts={tagCounts}
              displayTagToNormalized={displayTagToNormalized}
              onApply={setFilters}
              priceMode={priceMode}
              setPriceMode={(val) => {
                setPriceMode(val);
                setFilters((prev) => ({ ...prev, priceMode: val }));
              }}
              isGeojsonVisible={isGeojsonVisible}
              setIsGeojsonVisible={setIsGeojsonVisible}
              modeType={modeType}
              setModeType={setModeType}
              saleOrRent={saleOrRent}
              setSaleOrRent={setSaleOrRent}
              developers={developers}
              developerCounts={developerCounts}
              ratingOptions={ratingOptions}
              ratingCounts={ratingCounts}
              initialFilters={filters}
              compact={true}
              propSidebarOpen={propSidebarOpen}
              onTogglePropSidebar={() => setPropSidebarOpen((o) => !o)}
            />
          </Box>

          {/* Map Container */}
          <Box
            sx={{
              position: "relative",
              height: "100%",
              width: "100%",
              mt: "120px",
            }}
          >
            <ClusterMap
              data={filteredData}
              geojsonData={geojsonData}
              showGeojsonLayer={isGeojsonVisible}
              flyToLocation={flyToLocation}
              onPriceStatsUpdate={setPriceStats}
              basemap={basemap}
              priceMode={priceMode}
            />

            {/* Mobile Legend - Bottom Left */}
            <PriceLegend
              min={priceStats.min}
              avg={priceStats.avg}
              max={priceStats.max}
              priceMode={priceMode}
              isMobile={true}
            />

            {/* Mobile Area/Cluster Toggle - Bottom Right */}
            <Box
              sx={{
                position: "absolute",
                bottom: 20,
                right: 20,
                zIndex: 1000,
                display: "flex",
                flexDirection: "column",
                gap: 1,
              }}
            >
              <Box
                sx={{
                  bgcolor: "#181818",
                  borderRadius: 1,
                  border: "1px solid #333",
                  overflow: "hidden",
                }}
              >
                <Box
                  onClick={() => setIsGeojsonVisible(true)}
                  sx={{
                    px: 2,
                    py: 1,
                    bgcolor: isGeojsonVisible ? "#29b6f6" : "transparent",
                    color: isGeojsonVisible ? "#fff" : "#ccc",
                    fontSize: 12,
                    fontWeight: 600,
                    cursor: "pointer",
                    borderBottom: "1px solid #333",
                  }}
                >
                  AREA
                </Box>
                <Box
                  onClick={() => setIsGeojsonVisible(false)}
                  sx={{
                    px: 2,
                    py: 1,
                    bgcolor: !isGeojsonVisible ? "#29b6f6" : "transparent",
                    color: !isGeojsonVisible ? "#fff" : "#ccc",
                    fontSize: 12,
                    fontWeight: 600,
                    cursor: "pointer",
                  }}
                >
                  CLUSTER
                </Box>
              </Box>
            </Box>
          </Box>
        </>
      ) : (
        /* Desktop Layout */
        <>
          <FilterBar
            types={propertyTypes}
            tags={extractedTags}
            tagCounts={tagCounts}
            displayTagToNormalized={displayTagToNormalized}
            onApply={setFilters}
            priceMode={priceMode}
            setPriceMode={(val) => {
              setPriceMode(val);
              setFilters((prev) => ({ ...prev, priceMode: val }));
            }}
            isGeojsonVisible={isGeojsonVisible}
            setIsGeojsonVisible={setIsGeojsonVisible}
            modeType={modeType}
            setModeType={setModeType}
            saleOrRent={saleOrRent}
            setSaleOrRent={setSaleOrRent}
            developers={developers}
            developerCounts={developerCounts}
            ratingOptions={ratingOptions}
            ratingCounts={ratingCounts}
            initialFilters={filters}
            compact={false}
            propSidebarOpen={propSidebarOpen}
            onTogglePropSidebar={() => setPropSidebarOpen((o) => !o)}
          />

          <Box
            sx={{
              position: "fixed",
              top: 24,
              right: 32,
              zIndex: 1300,
              display: "flex",
              flexDirection: "row",
              gap: 2,
              alignItems: "center",
              width: 480,
              maxWidth: 600,
            }}
          >
            <Box sx={{ flex: 1, minWidth: 320 }}>
              <SearchBar
                value={searchValue}
                onChange={setSearchValue}
                onSelect={handleLocationSelect}
                options={locationOptions}
                compact={false}
                setFlyToLocation={setFlyToLocation}
              />
            </Box>
            <Box
              sx={{ minWidth: 120, bgcolor: "#222", borderRadius: 1, ml: 2 }}
            >
              <select
                value={basemap}
                onChange={(e) => setBasemap(e.target.value)}
                style={{
                  color: "#ccc",
                  background: "transparent",
                  border: "none",
                  fontSize: 13,
                  padding: "8px 12px",
                  borderRadius: 6,
                  width: "100%",
                }}
              >
                <option value="mapbox://styles/mapbox/dark-v10">Dark</option>
                <option value="mapbox://styles/mapbox/light-v10">Light</option>
                <option value="mapbox://styles/mapbox/streets-v11">
                  Streets
                </option>
                <option value="mapbox://styles/mapbox/satellite-streets-v12">
                  Satellite
                </option>
              </select>
            </Box>
          </Box>

          <Box sx={{ position: "relative", height: "100%", width: "100%" }}>
            <ClusterMap
              data={filteredData}
              geojsonData={geojsonData}
              showGeojsonLayer={isGeojsonVisible}
              flyToLocation={flyToLocation}
              onPriceStatsUpdate={setPriceStats}
              basemap={basemap}
              priceMode={priceMode}
            />

            <PropertySidebar
              items={filteredData}
              priceMode={priceMode}
              onSelect={handleSidebarSelect}
              open={propSidebarOpen}
              onClose={() => setPropSidebarOpen(false)}
            />

            <PriceLegend
              min={priceStats.min}
              avg={priceStats.avg}
              max={priceStats.max}
              priceMode={priceMode}
              isMobile={false}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export default App;
